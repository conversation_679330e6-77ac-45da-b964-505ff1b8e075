import { NextResponse } from "next/server";
import { auth } from "@/auth";
import dbConnect from "@/libs/mongoDb/connectToLuyariDB";
import { Building } from "@/libs/mongoDb/models/Building";
import mongoose from "mongoose";

/**
 * GET /api/client/projects - Get client's assigned projects
 * Query parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - search: Search term for projectTitle or buildingTitle
 * - buildingType: Filter by building type
 * - sortBy: Sort field (default: 'createdAt')
 * - sortOrder: Sort order 'asc' or 'desc' (default: 'desc')
 */
export async function GET(request) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    // Only client users can access this endpoint
    if (session.user.role !== 'client') {
      return NextResponse.json(
        { message: "Access denied. Client role required." },
        { status: 403 }
      );
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const search = searchParams.get('search') || '';
    const buildingType = searchParams.get('buildingType') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const userProjects = session.user.projects || [];

    if (userProjects.length === 0) {
      return NextResponse.json({
        buildings: [],
        pagination: {
          page: 1,
          limit,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      });
    }

    // Convert project IDs to ObjectIds
    const projectObjectIds = userProjects
      .filter(id => mongoose.Types.ObjectId.isValid(id))
      .map(id => new mongoose.Types.ObjectId(id));

    // Build query for client's assigned projects
    const query = {
      _id: { $in: projectObjectIds },
      buildingRole: 'client'
    };
    
    // Apply search filters
    if (search) {
      query.$and = [
        { _id: { $in: projectObjectIds } },
        { buildingRole: 'client' },
        {
          $or: [
            { projectTitle: { $regex: search, $options: 'i' } },
            { buildingTitle: { $regex: search, $options: 'i' } },
            { desc: { $regex: search, $options: 'i' } }
          ]
        }
      ];
      delete query._id;
      delete query.buildingRole;
    }
    
    if (buildingType) {
      if (query.$and) {
        query.$and.push({ buildingType });
      } else {
        query.buildingType = buildingType;
      }
    }

    // Build sort object
    const sort = {};
    const validSortFields = ['createdAt', 'updatedAt', 'buildingTitle', 'buildingType', 'projectTitle'];
    if (validSortFields.includes(sortBy)) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1; // Default sort
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Get buildings with pagination
    const buildings = await Building.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Building.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      buildings,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error("Error fetching client projects:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}
