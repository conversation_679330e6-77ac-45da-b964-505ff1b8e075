import { auth } from "@/auth";
import UserService from "@/libs/userSchema";
import { NextResponse } from "next/server";
import dbConnect from "@/libs/mongoDb/connectToLuyariDB";
import { Building } from "@/libs/mongoDb/models/Building";
import mongoose from "mongoose";

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await UserService.getUserByEmail(session.user.email);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    let projects = [];

    // Fetch actual projects for client users
    if (user.role === 'client' && user.projects && user.projects.length > 0) {
      await dbConnect();

      // Convert project IDs to ObjectIds and filter valid ones
      const projectObjectIds = user.projects
        .filter(id => mongoose.Types.ObjectId.isValid(id))
        .map(id => new mongoose.Types.ObjectId(id));

      if (projectObjectIds.length > 0) {
        const buildings = await Building.find({
          _id: { $in: projectObjectIds },
          buildingRole: 'client'
        })
        .select('_id projectTitle buildingTitle buildingType createdAt updatedAt buildingSummary')
        .lean();

        projects = buildings.map(building => ({
          id: building._id.toString(),
          projectTitle: building.projectTitle,
          buildingTitle: building.buildingTitle,
          buildingType: building.buildingType,
          createdAt: building.createdAt,
          updatedAt: building.updatedAt,
          buildingSummary: building.buildingSummary
        }));
      }
    }

    // Mock invites data - replace with actual invites fetching logic when implemented
    const invites = [
      {
        id: 'inv1',
        email: '<EMAIL>',
        projectId: 'proj1',
        status: 'pending',
        createdAt: new Date().toISOString()
      }
    ];

    return NextResponse.json({
      user: {
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        email: user.email,
        website: user.website,
        image: user.image,
        role: user.role,
        projects: user.projects || []
      },
      projects,
      invites
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request) {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { username, firstName, lastName, phone, website } = body;

    // Validate input
    if (!username || username.trim().length < 2) {
      return NextResponse.json({ error: 'Username must be at least 2 characters' }, { status: 400 });
    }

    // Update user profile
    const updatedUser = await UserService.updateUserByEmail(session.user.email, {
      username: username.trim(),
      firstName: firstName?.trim() || '',
      lastName: lastName?.trim() || '',
      phone: phone?.trim() || '',
      website: website?.trim() || ''
    });

    if (!updatedUser) {
      return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
    }

    return NextResponse.json({
      user: {
        username: updatedUser.username,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        phone: updatedUser.phone,
        email: updatedUser.email,
        website: updatedUser.website,
        image: updatedUser.image
      }
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
