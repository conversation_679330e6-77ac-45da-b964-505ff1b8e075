'use client'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useEffect, useRef, useState, Suspense } from 'react'
import React from 'react'
import { Html } from '@react-three/drei'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'

export default function ExperienceModels({ data }) {
  const { experienceState } = useExperienceContext()
  const { scene } = useThree()
  
  const refModel = useRef(null)
  const refHideLevel = useRef(null)
  const [levelsToHideList, setLevelsToHideList] = useState([])
  
  // Loading state management
  const [totalFiles, setTotalFiles] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  // Calculate total files and set loading state
  useEffect(() => {
    if (!data) {
      setIsLoading(false)
      return
    }

    const allFiles = [
      ...(data?.modelsFiles || []),
      ...(data?.hideLevel || []),
      ...(data?.supportFiles || []),
      ...(data?.roomSnaps || [])
    ].filter(Boolean)

    setTotalFiles(allFiles.length)
    setIsLoading(allFiles.length > 0)
  }, [data])

  // Custom loading fallback component
  const ModelLoadingFallback = ({ fileName }) => (
    <Html center>
      <div className="flex flex-col items-center justify-center text-white">
        <LoadingSpinner />
        <div className="mt-4 text-center">
          <div className="text-lg font-medium">Loading 3D Models</div>
          <div className="text-sm opacity-75 mt-2">
            Loading {totalFiles} files...
          </div>
          {fileName && (
            <div className="text-xs opacity-60 mt-1 max-w-xs truncate">
              {fileName}
            </div>
          )}
        </div>
      </div>
    </Html>
  )

  // console.log('ExperienceModels Loading:', {
  //   totalFiles,
  //   isLoading
  // })

  return (
    <>
      <Suspense fallback={<ModelLoadingFallback />}>
        <group
          name="ExperienceModel"
          ref={refModel}
          position={Array.isArray(data?.position) ? data.position : data?.position?.split(',').map(i => Number(i)) || [0, 0, 0]}
        >
          {/* Main model files */}
          {data?.modelsFiles?.map((model, index) => (
            <ExperienceGLTFLoader key={`models_${index}`} path={model} />
          ))}

          {/* Hide level models */}
          <group ref={refHideLevel} name="hideLevel">
            {data?.hideLevel?.map((model, index) => (
              <ExperienceGLTFLoader key={`hideLevel_${index}`} path={model} />
            ))}
          </group>

          {/* Support files */}
          {data?.supportFiles?.map((model, index) => (
            <ExperienceGLTFLoader key={`support_${index}`} path={model} />
          ))}

          {/* Room snaps */}
          <group name="roomSnaps">
            {data?.roomSnaps?.map((model, index) => (
              <ExperienceGLTFLoader key={`roomSnaps_${index}`} path={model} />
            ))}
          </group>
        </group>

        {/* Environment - only show when models are loaded */}
        <Environment preset="city" />
      </Suspense>

      {/* Debug info */}
      {/* {process.env.NODE_ENV === 'development' && (
        <Html position={[0, -20, 0]} center>
          <div style={{ 
            background: 'rgba(0,0,0,0.7)', 
            color: 'white', 
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px'
          }}>
            <div>Total files: {totalFiles}</div>
            <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
            <div>Using Suspense for loading states</div>
          </div>
        </Html>
      )} */}
    </>
  )
}