'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PageWrapper from '@/components/PageWrapper'
import Image from 'next/image'
import Link from 'next/link'
import {
  HiSearch,
  HiFilter,
  HiAdjustments
} from 'react-icons/hi'
import { IoCarOutline, IoBedOutline } from "react-icons/io5"
import { LuBath } from "react-icons/lu"
import { SiLevelsdotfyi } from "react-icons/si"

export default function ProjectsPage() {
  const [buildings, setBuildings] = useState([])
  const [filteredBuildings, setFilteredBuildings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCollection, setSelectedCollection] = useState('')
  const [selectedBuildingType, setSelectedBuildingType] = useState('')
  const [bedsFilter, setBedsFilter] = useState('')
  const [bathsFilter, setBathsFilter] = useState('')
  const [carsFilter, setCarsFilter] = useState('')
  const [levelsFilter, setLevelsFilter] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  // Filter options
  const [collections, setCollections] = useState([])
  const [buildingTypes, setBuildingTypes] = useState([])
  const [summaryRanges, setSummaryRanges] = useState({
    beds: { min: 0, max: 0 },
    baths: { min: 0, max: 0 },
    cars: { min: 0, max: 0 },
    levels: { min: 0, max: 0 }
  })

  // Fetch buildings and filter options
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        // Fetch buildings
        const buildingsResponse = await fetch('/api/buildings')
        if (!buildingsResponse.ok) {
          throw new Error('Failed to fetch buildings')
        }
        const buildingsData = await buildingsResponse.json()
        setBuildings(buildingsData.buildings)
        setFilteredBuildings(buildingsData.buildings)

        // Fetch filter options
        const collectionsResponse = await fetch('/api/buildings/collections')
        if (collectionsResponse.ok) {
          const collectionsData = await collectionsResponse.json()
          setCollections(collectionsData.collections || [])
          setBuildingTypes(collectionsData.buildingTypes || [])
          setSummaryRanges(collectionsData.summaryRanges || summaryRanges)
        }

        setError('')
      } catch (err) {
        setError(err.message)
        setBuildings([])
        setFilteredBuildings([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Apply filters
  useEffect(() => {
    let filtered = [...buildings]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(building =>
        building.buildingTitle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        building.projectTitle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        building.desc?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Collection filter
    if (selectedCollection) {
      filtered = filtered.filter(building =>
        building.collections?.includes(selectedCollection)
      )
    }

    // Building type filter
    if (selectedBuildingType) {
      filtered = filtered.filter(building =>
        building.buildingType === selectedBuildingType
      )
    }

    // Summary filters
    if (bedsFilter) {
      filtered = filtered.filter(building =>
        building.buildingSummary?.beds === parseInt(bedsFilter)
      )
    }

    if (bathsFilter) {
      filtered = filtered.filter(building =>
        building.buildingSummary?.baths === parseInt(bathsFilter)
      )
    }

    if (carsFilter) {
      filtered = filtered.filter(building =>
        building.buildingSummary?.cars === parseInt(carsFilter)
      )
    }

    if (levelsFilter) {
      filtered = filtered.filter(building =>
        building.buildingSummary?.levels === parseInt(levelsFilter)
      )
    }

    setFilteredBuildings(filtered)
  }, [buildings, searchTerm, selectedCollection, selectedBuildingType, bedsFilter, bathsFilter, carsFilter, levelsFilter])

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCollection('')
    setSelectedBuildingType('')
    setBedsFilter('')
    setBathsFilter('')
    setCarsFilter('')
    setLevelsFilter('')
  }

  const hasActiveFilters = searchTerm || selectedCollection || selectedBuildingType || bedsFilter || bathsFilter || carsFilter || levelsFilter

  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-2 py-2'>
        {/* Header Section */}
        <div className="text-center mb-2">
          <h1 className='chaumet-heading text-2xl md:text-5xl mb-2 text-neutral-900'>
            Our <span className="font-light">Projects</span>
          </h1>
          <div className="chaumet-divider w-16 mx-auto" />
          <p className="text-neutral-600 font-light tracking-wide mt-1 max-w-2xl mx-auto">
            Explore our portfolio of architectural visualizations, where precision meets artistry
          </p>
        </div>

        {/* Search and Filter Controls */}
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <HiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light text-lg"
              />
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center px-6 py-3 border rounded-lg transition-colors ${
                showFilters || hasActiveFilters
                  ? 'bg-neutral-900 text-white border-neutral-900'
                  : 'bg-white text-neutral-600 border-neutral-200 hover:border-neutral-300'
              }`}
            >
              <HiAdjustments className="w-5 h-5 mr-2" />
              Filters
              {hasActiveFilters && (
                <span className="ml-2 bg-white text-neutral-900 text-xs px-2 py-1 rounded-full">
                  Active
                </span>
              )}
            </button>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-neutral-50 border border-neutral-200 rounded-lg p-6"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-light text-neutral-900">Filter Projects</h3>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className="text-sm text-neutral-600 hover:text-neutral-900 transition-colors"
                  >
                    Clear All
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Collection Filter */}
                {collections.length > 0 && (
                  <div>
                    <label className="block text-sm font-light text-neutral-700 mb-2">
                      Collection
                    </label>
                    <select
                      value={selectedCollection}
                      onChange={(e) => setSelectedCollection(e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light"
                    >
                      <option value="">All Collections</option>
                      {collections.map(collection => (
                        <option key={collection} value={collection}>
                          {collection}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Building Type Filter */}
                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    Building Type
                  </label>
                  <select
                    value={selectedBuildingType}
                    onChange={(e) => setSelectedBuildingType(e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light"
                  >
                    <option value="">All Types</option>
                    {buildingTypes.map(type => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Beds Filter */}
                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    Bedrooms
                  </label>
                  <select
                    value={bedsFilter}
                    onChange={(e) => setBedsFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light"
                  >
                    <option value="">Any</option>
                    {Array.from({ length: summaryRanges.beds.max - summaryRanges.beds.min + 1 }, (_, i) => summaryRanges.beds.min + i).map(num => (
                      <option key={num} value={num}>
                        {num} {num === 1 ? 'Bedroom' : 'Bedrooms'}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Bathrooms Filter */}
                <div>
                  <label className="block text-sm font-light text-neutral-700 mb-2">
                    Bathrooms
                  </label>
                  <select
                    value={bathsFilter}
                    onChange={(e) => setBathsFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:border-transparent font-light"
                  >
                    <option value="">Any</option>
                    {Array.from({ length: summaryRanges.baths.max - summaryRanges.baths.min + 1 }, (_, i) => summaryRanges.baths.min + i).map(num => (
                      <option key={num} value={num}>
                        {num} {num === 1 ? 'Bathroom' : 'Bathrooms'}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Results Count */}
        <div className="flex justify-between items-center">
          <p className="text-neutral-600 font-light">
            {loading ? 'Loading...' : `${filteredBuildings.length} project${filteredBuildings.length !== 1 ? 's' : ''} found`}
          </p>
        </div>

        {/* Error State */}
        {error && (
          <div className="text-red-600 text-center p-4 bg-red-50 rounded-lg">
            {error}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-neutral-900"></div>
          </div>
        )}

        {/* Projects Grid */}
        {!loading && filteredBuildings.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 w-full overflow-y-auto'
          >
            {filteredBuildings.map((project, index) =>
              <motion.div
                key={project?._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className='group flex flex-col bg-white border border-neutral-100 hover:border-neutral-200 transition-all duration-500 hover:shadow-lg'
              >
                <div className='relative w-full aspect-[4/3] overflow-hidden'>
                  <Image
                    src={project?.renders?.[0]?.url}
                    alt={project?.buildingTitle || 'Project'}
                    fill
                    className='object-cover transition-transform duration-700 group-hover:scale-105'
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-500" />

                  {/* Collection Badge */}
                  {project.collections && project.collections.length > 0 && (
                    <div className="absolute top-3 left-3">
                      <span className="bg-white/90 text-neutral-700 text-xs px-2 py-1 rounded-full font-light">
                        {project.collections[0]}
                      </span>
                    </div>
                  )}
                </div>

                <div className='p-6 flex flex-col flex-grow'>
                  <h2 className='text-xl font-light text-neutral-900 mb-2 tracking-wide group-hover:text-neutral-700 transition-colors duration-300'>
                    {project?.buildingTitle}
                  </h2>
                  <p className='text-sm text-neutral-500 font-light tracking-wider uppercase mb-3'>
                    {project?.buildingType}
                  </p>

                  {/* Building Summary */}
                  {project.buildingSummary && (
                    <div className="grid grid-cols-4 gap-2 mb-4 p-3 bg-neutral-50 rounded-lg">
                      <div className="text-center">
                        <IoBedOutline className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                        <span className="text-xs text-neutral-600">{project.buildingSummary.beds}</span>
                      </div>
                      <div className="text-center">
                        <LuBath className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                        <span className="text-xs text-neutral-600">{project.buildingSummary.baths}</span>
                      </div>
                      <div className="text-center">
                        <IoCarOutline className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                        <span className="text-xs text-neutral-600">{project.buildingSummary.cars}</span>
                      </div>
                      <div className="text-center">
                        <SiLevelsdotfyi className="w-4 h-4 mx-auto text-neutral-500 mb-1" />
                        <span className="text-xs text-neutral-600">{project.buildingSummary.levels}</span>
                      </div>
                    </div>
                  )}

                  {/* Subtle hover indicator */}
                  <Link href={`/projects/${project?._id}`} className="mt-auto pt-4 border-t border-neutral-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <span className="text-xs text-neutral-400 font-light tracking-widest uppercase">
                      View Project
                    </span>
                  </Link>
                </div>
              </motion.div>
            )}
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && filteredBuildings.length === 0 && !error && (
          <div className="text-center py-12">
            <div className="text-neutral-300 mb-4">
              <HiFilter className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-light text-neutral-600 mb-2">No Projects Found</h3>
            <p className="text-neutral-500 font-light mb-4">
              Try adjusting your search criteria or filters
            </p>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="chaumet-button border-neutral-900 text-neutral-900 group"
              >
                <span className="relative z-10 group-hover:text-white transition-colors duration-500">
                  Clear Filters
                </span>
              </button>
            )}
          </div>
        )}
      </div>
    </PageWrapper>
  )
}
