import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import UserService from '@/libs/userSchema'
import { ObjectId } from 'mongodb'

// GET /api/admin/users/[id] - Get specific user
export async function GET(request, { params }) {
  try {
    const session = await auth()

    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const { id } = await params

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    const user = await UserService.getUserById(new ObjectId(id))
    
    return NextResponse.json(user)
  } catch (error) {
    console.error('Error in GET /api/admin/users/[id]:', error)
    
    if (error.message === 'User not found') {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/users/[id] - Update user
export async function PUT(request, { params }) {
  try {
    const session = await auth()

    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    // Validate allowed updates
    const allowedFields = ['role', 'phone', 'projects', 'username', 'firstName', 'lastName']
    const updates = {}
    
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updates[field] = body[field]
      }
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      )
    }

    // Validate role if being updated
    if (updates.role && !['admin', 'user', 'client'].includes(updates.role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be "admin", "user", or "client"' },
        { status: 400 }
      )
    }

    // Validate projects assignment
    if (updates.projects !== undefined) {
      // Ensure projects is an array
      if (!Array.isArray(updates.projects)) {
        return NextResponse.json(
          { error: 'Projects must be an array' },
          { status: 400 }
        )
      }

      // Validate that projects are only assigned to client users
      if (updates.projects.length > 0) {
        // Get current user to check role
        const currentUser = await UserService.getUserById(new ObjectId(id))
        const targetRole = updates.role || currentUser.role

        if (targetRole !== 'client') {
          return NextResponse.json(
            { error: 'Projects can only be assigned to client users' },
            { status: 400 }
          )
        }
      }
    }

    // Prevent admin from demoting themselves
    if (updates.role === 'user' && id === session.user.id) {
      return NextResponse.json(
        { error: 'Cannot change your own role' },
        { status: 400 }
      )
    }

    const updatedUser = await UserService.updateUser(new ObjectId(id), updates, new ObjectId(session.user.id))
    
    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error('Error in PUT /api/admin/users/[id]:', error)
    
    if (error.message === 'User not found') {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/users/[id] - Delete specific user
export async function DELETE(request, { params }) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const { id } = await params

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    // Prevent admin from deleting themselves
    if (id === session.user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    const result = await UserService.deleteUser(new ObjectId(id))
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in DELETE /api/admin/users/[id]:', error)
    
    if (error.message === 'User not found') {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
