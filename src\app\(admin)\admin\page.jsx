import PageWrapper from '@/components/PageWrapper'
import { auth } from '@/auth'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { HiOfficeBuilding, HiUsers, HiPlus, <PERSON><PERSON>ye, HiArrowRight } from 'react-icons/hi'
import React from 'react'

export default async function AdminPage() {
  const session = await auth()

  // Check if user is authenticated and is admin
  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin')
  }

  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-6 overflow-y-auto'>
        <div className='border-b border-neutral-200 pb-6'>
          <h1 className='chaumet-heading text-4xl text-neutral-900'>Admin Dashboard</h1>
          <p className='text-lg text-neutral-600 font-light mt-2'>Manage users, buildings, and system settings</p>
          <div className="chaumet-divider mt-4" style={{ width: '4rem' }} />
        </div>

        {/* Quick Actions */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div className='bg-white rounded-lg border border-neutral-200 p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                <HiUsers className='h-8 w-8 text-neutral-600 mr-3' />
                <div>
                  <h3 className='chaumet-heading text-xl text-neutral-900'>User Management</h3>
                  <p className='text-sm text-neutral-600 font-light'>Manage user accounts and permissions</p>
                </div>
              </div>
            </div>
            <div className='flex space-x-3'>
              <Link
                href='/admin/users'
                className='chaumet-button border-neutral-900 text-neutral-900 group flex-1'
              >
                <span className='relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center'>
                  <HiUsers className='w-4 h-4 mr-2' />
                  Manage Users
                </span>
              </Link>
            </div>
          </div>

          <div className='bg-white rounded-lg border border-neutral-200 p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                <HiOfficeBuilding className='h-8 w-8 text-neutral-600 mr-3' />
                <div>
                  <h3 className='chaumet-heading text-xl text-neutral-900'>Building Management</h3>
                  <p className='text-sm text-neutral-600 font-light'>Manage building projects and files</p>
                </div>
              </div>
            </div>
            <div className='flex space-x-3'>
              <Link
                href='/admin/buildings'
                className='chaumet-button border-neutral-900 text-neutral-900 group flex-1'
              >
                <span className='relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center'>
                  <HiEye className='w-4 h-4 mr-2' />
                  View Buildings
                </span>
              </Link>
              <Link
                href='/admin/buildings/create'
                className='chaumet-button border-neutral-600 text-neutral-600 group'
              >
                <span className='relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center'>
                  <HiPlus className='w-4 h-4 mr-2' />
                  Create
                </span>
              </Link>
            </div>
          </div>
        </div>

        {/* System Overview */}
        <div className='bg-white rounded-lg border border-neutral-200'>
          <div className='px-6 py-4 border-b border-neutral-200'>
            <h2 className='chaumet-heading text-xl text-neutral-900'>System Overview</h2>
            <p className='text-sm text-neutral-600 font-light mt-1'>Quick overview of system status and recent activity</p>
          </div>
          <div className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='text-center'>
                <div className='text-2xl font-light text-neutral-900 mb-1'>Active Users</div>
                <div className='text-sm text-neutral-600'>System users with access</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-light text-neutral-900 mb-1'>Buildings</div>
                <div className='text-sm text-neutral-600'>Total building projects</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-light text-neutral-900 mb-1'>Client Projects</div>
                <div className='text-sm text-neutral-600'>Active client assignments</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
