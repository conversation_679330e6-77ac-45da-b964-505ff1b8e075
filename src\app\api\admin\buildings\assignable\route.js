import { NextResponse } from "next/server";
import { auth } from "@/auth";
import dbConnect from "@/libs/mongoDb/connectToLuyariDB";
import { Building } from "@/libs/mongoDb/models/Building";

/**
 * GET /api/admin/buildings/assignable - Get buildings available for client assignment
 * Returns buildings with buildingRole='client' that can be assigned to client users
 * Only accessible by admin users
 */
export async function GET(request) {
  try {
    const session = await auth();
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    // Build query for assignable buildings
    const query = {
      buildingRole: 'client' // Only buildings designated for client access
    };

    // Add search functionality if search term provided
    if (search) {
      query.$or = [
        { projectTitle: { $regex: search, $options: 'i' } },
        { buildingTitle: { $regex: search, $options: 'i' } }
      ];
    }

    // Get buildings with only necessary fields for dropdown
    const buildings = await Building.find(query)
      .select('_id projectTitle buildingTitle buildingType createdAt')
      .sort({ projectTitle: 1, buildingTitle: 1 })
      .lean();

    // Format buildings for dropdown display
    const formattedBuildings = buildings.map(building => ({
      id: building._id.toString(),
      value: building._id.toString(),
      label: `${building.projectTitle} - ${building.buildingTitle}`,
      projectTitle: building.projectTitle,
      buildingTitle: building.buildingTitle,
      buildingType: building.buildingType,
      createdAt: building.createdAt
    }));

    return NextResponse.json({
      buildings: formattedBuildings,
      total: formattedBuildings.length
    });

  } catch (error) {
    console.error("Error fetching assignable buildings:", error);
    return NextResponse.json(
      { error: "Failed to fetch assignable buildings", details: error.message },
      { status: 500 }
    );
  }
}
