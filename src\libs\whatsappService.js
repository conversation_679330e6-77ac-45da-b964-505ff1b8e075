// WhatsApp Service for sending messages
// This service provides multiple methods for WhatsApp integration

/**
 * Send WhatsApp message using web URL method (client-side)
 * @param {string} phone - Phone number with country code
 * @param {string} message - Message to send
 * @returns {string} WhatsApp URL
 */
export function generateWhatsAppURL(phone, message) {
  const encodedMessage = encodeURIComponent(message)
  return `https://wa.me/${phone}?text=${encodedMessage}`
}

/**
 * Format contact form data into WhatsApp message
 * @param {Object} formData - Form data object
 * @returns {string} Formatted message
 */
export function formatContactMessage(formData) {
  const { name, email, phone, message } = formData
  
  return `🔔 New Contact Form Submission

👤 Name: ${name}
📧 Email: ${email}
📱 Phone: ${phone || 'Not provided'}

💬 Message:
${message}

---
Sent from Luyari.com Contact Form
Time: ${new Date().toLocaleString()}`
}

/**
 * Send WhatsApp message using webhook service (if available)
 * This is a placeholder for future WhatsApp Business API integration
 * @param {string} phone - Phone number
 * @param {string} message - Message to send
 * @returns {Promise<Object>} Result object
 */
export async function sendWhatsAppViaAPI(phone, message) {
  try {
    // This is where you would integrate with WhatsApp Business API
    // For now, we'll simulate the API call
    
    // Example implementation with a webhook service:
    /*
    const response = await fetch('YOUR_WHATSAPP_WEBHOOK_URL', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.WHATSAPP_API_TOKEN}`
      },
      body: JSON.stringify({
        phone: phone,
        message: message
      })
    })
    
    if (response.ok) {
      return { success: true, message: 'WhatsApp message sent via API' }
    } else {
      throw new Error('API request failed')
    }
    */
    
    // For now, we'll just log and return success
    console.log(`WhatsApp message would be sent to ${phone}:`, message)
    
    return {
      success: true,
      message: 'WhatsApp message logged (API integration pending)',
      method: 'simulation'
    }
  } catch (error) {
    console.error('WhatsApp API error:', error)
    return {
      success: false,
      error: 'Failed to send WhatsApp message via API'
    }
  }
}

/**
 * Send WhatsApp message using URL method (opens WhatsApp)
 * This method generates a URL that can be used client-side
 * @param {string} phone - Phone number
 * @param {string} message - Message to send
 * @returns {Object} Result with URL
 */
export function sendWhatsAppViaURL(phone, message) {
  try {
    const url = generateWhatsAppURL(phone, message)
    
    return {
      success: true,
      message: 'WhatsApp URL generated successfully',
      url: url,
      method: 'url'
    }
  } catch (error) {
    console.error('WhatsApp URL generation error:', error)
    return {
      success: false,
      error: 'Failed to generate WhatsApp URL'
    }
  }
}

/**
 * Main WhatsApp sending function that tries multiple methods
 * @param {string} phone - Phone number
 * @param {string} message - Message to send
 * @returns {Promise<Object>} Result object
 */
export async function sendWhatsAppMessage(phone, message) {
  // Try API method first (if available)
  const apiResult = await sendWhatsAppViaAPI(phone, message)
  
  if (apiResult.success) {
    return apiResult
  }
  
  // Fallback to URL method
  const urlResult = sendWhatsAppViaURL(phone, message)
  
  return {
    ...urlResult,
    fallback: true,
    apiError: apiResult.error
  }
}

/**
 * Client-side function to open WhatsApp with pre-filled message
 * This should be called from the browser
 * @param {string} phone - Phone number
 * @param {string} message - Message to send
 */
export function openWhatsApp(phone, message) {
  if (typeof window !== 'undefined') {
    const url = generateWhatsAppURL(phone, message)
    window.open(url, '_blank')
  }
}
