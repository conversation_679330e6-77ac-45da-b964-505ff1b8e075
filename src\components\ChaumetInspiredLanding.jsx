'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { HiOutlineArrowNarrowRight } from 'react-icons/hi';
import WhatsAPPComponent from './WhatsAppComponent';
import Image from 'next/image';

export default function ChaumetInspiredLanding() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [autoScrollPaused, setAutoScrollPaused] = useState(false);
  const [isVisible, setIsVisible] = useState({});
  const sectionRefs = useRef({});

  // Contact form state
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitStatus, setSubmitStatus] = useState(''); // 'success' or 'error'

  // Contact form handlers
  const handleContactInputChange = (e) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateContactForm = () => {
    const errors = {};

    if (!contactForm.name.trim()) {
      errors.name = 'Name is required';
    } else if (contactForm.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    if (!contactForm.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactForm.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!contactForm.message.trim()) {
      errors.message = 'Message is required';
    } else if (contactForm.message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters';
    }

    return errors;
  };

  const handleContactSubmit = async (e) => {
    e.preventDefault();
    console.log('ChaumetInspiredLanding form submitted:', contactForm);
    setSubmitMessage('');
    setSubmitStatus('');

    // Validate form
    const errors = validateContactForm();
    if (Object.keys(errors).length > 0) {
      setSubmitStatus('error');
      setSubmitMessage(Object.values(errors)[0]);
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactForm),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setSubmitMessage('Thank you! Your message has been sent successfully.');
        setContactForm({ name: '', email: '', message: '' });
      } else {
        setSubmitStatus('error');
        setSubmitMessage(data.error || 'Failed to send message. Please try again.');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setSubmitStatus('error');
      setSubmitMessage('An error occurred. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Hero carousel images with absolute URLs
  const heroImages = [
    '/hero/0005.jpg',
    '/hero/View_1-Lawn-Option-1.jpg',
    '/hero/View_3-Pool-Option-1.jpg',
  ];

  // Portfolio projects
  const portfolioProjects = [
    {
      image: '/hero/0003_Opt1.jpg',
      title: 'Modern Residential',
      category: 'Exterior Visualization'
    },
    {
      image: '/hero/0003_Opt2.jpg',
      title: 'Contemporary Villa',
      category: 'Exterior Visualization'
    },
    {
      image: '/hero/0005.jpg',
      title: 'Urban Apartment',
      category: 'Interior Visualization'
    },
    {
      image: '/hero/0006.jpg',
      title: 'Luxury Residence',
      category: 'Exterior Visualization'
    }
  ];

  // Services data
  const services = [
    {
      title: "Architectural Visualization",
      description: "Transform blueprints into photorealistic 3D renderings that bring your designs to life before construction begins."
    },
    {
      title: "Interior Visualization",
      description: "Explore interior spaces with detailed visualizations that showcase materials, lighting, and spatial relationships."
    },
    {
      title: "Design Development",
      description: "Refine your architectural concepts with iterative visualizations that help clients understand design decisions."
    }
  ];

  // Auto-scroll for hero carousel
  useEffect(() => {
    if (autoScrollPaused) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [autoScrollPaused, heroImages.length]);

  // Handle manual navigation
  const goToSlide = (index) => {
    setCurrentSlide(index);
    setAutoScrollPaused(true);

    // Resume auto-scroll after 15 seconds
    setTimeout(() => {
      setAutoScrollPaused(false);
    }, 15000);
  };

  // Intersection observer for animations
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(prev => ({ ...prev, [entry.target.id]: true }));
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe all section refs
    Object.keys(sectionRefs.current).forEach(key => {
      if (sectionRefs.current[key]) {
        observer.observe(sectionRefs.current[key]);
      }
    });

    return () => {
      Object.keys(sectionRefs.current).forEach(key => {
        if (sectionRefs.current[key]) {
          observer.unobserve(sectionRefs.current[key]);
        }
      });
    };
  }, []);

  // Register a section ref
  const registerSectionRef = (id, el) => {
    if (el && !sectionRefs.current[id]) {
      sectionRefs.current[id] = el;
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-y-auto">
      {/* Hero Section */}
      <section className="relative w-full h-screen">
        {/* Hero Carousel */}
        <div className='flex z-10 absolute w-full h-full'>
          {heroImages.map((image, index) => (
            // (console.log(image)),
            <div 
              key={index}
              className={`absolute w-full h-full overflow-hidden transition-opacity duration-1000 ${currentSlide === index ? 'opacity-100' : 'opacity-0'}`}
               style={{
                backgroundImage: `url(${image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
              aria-label={`Architectural visualization ${index + 1}`}
            >
              <Image
                key={index}
                src={image}
                alt={`Architectural visualization ${index + 1}`}
                fill
                priority={index === 0}
                sizes="100vw"
                quality={90}
                className="object-cover brightness-90"
              />
            </div>
          ))}
        </div>

        {/* Hero Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4 md:px-8">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-6xl font-thin text-white mb-6 tracking-wide"
          >
            <span className="block mb-2">Architectural</span>
            <span className="font-light">Visualization</span>
          </motion.h1>
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: '4rem' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="h-px bg-white mx-auto mb-6"
          />
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-white mb-10 max-w-2xl font-thin tracking-wide"
          >
            Transforming architectural concepts into photorealistic experiences with precision and artistry
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Link
              href="#portfolio"
              className="chaumet-button border-white text-white text-xs group"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500">DISCOVER</span>
            </Link>
          </motion.div>
        </div>

        {/* Carousel Navigation */}
        <div className="CarouselWrapp absolute bottom-10 left-0 right-0 z-20 flex justify-center space-x-3">
          {heroImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full cursor-pointer transition-colors ease-linear duration-300 ${
                currentSlide === index ? 'bg-white w-10 ' : 'bg-white/40 w-2'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </section>

      {/* Introduction Section */}
      <section
        id="intro"
        ref={(el) => registerSectionRef('intro', el)}
        className="py-20 px-4 md:px-8 bg-white"
      >
        <div className="max-w-5xl mx-auto text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isVisible.intro ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="chaumet-heading text-3xl md:text-4xl mb-6 text-neutral-900"
          >
            Where <span className="font-light">vision</span> meets <span className="font-light">reality</span>
          </motion.h2>
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={isVisible.intro ? { opacity: 1, width: '3rem' } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="chaumet-divider"
          />
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isVisible.intro ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg text-neutral-600 mb-12 max-w-3xl mx-auto font-extralight leading-relaxed"
          >
            We specialize in creating photorealistic architectural visualizations that help architects, designers, and developers communicate their vision with clarity and impact. Our meticulous attention to detail and commitment to artistic excellence ensure that every project is rendered with precision and beauty.
          </motion.p>
        </div>
      </section>

      {/* Portfolio Section */}
      <section
        id="portfolio"
        ref={(el) => registerSectionRef('portfolio', el)}
        className="py-20 px-4 md:px-8 bg-neutral-50"
      >
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible.portfolio ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="chaumet-heading text-3xl md:text-4xl mb-4 text-neutral-900"
            >
              Our <span className="font-light">Portfolio</span>
            </motion.h2>
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={isVisible.portfolio ? { opacity: 1, width: '3rem' } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="chaumet-divider"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {portfolioProjects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={isVisible.portfolio ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                className="group relative overflow-hidden"
              >
                <div
                  className="relative w-full pb-[75%]"
                >
                  <div
                    className="absolute inset-0 object-cover transition-transform duration-700 group-hover:scale-105"
                    style={{
                      backgroundImage: `url(${project.image})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                    aria-label={project.title}
                  ></div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent md:-opacity-0 opacity-100 group-hover:opacity-100 transition-opacity duration-500 flex items-end">
                  <div className="p-6 w-full">
                    <h3 className="text-xl font-light text-white">{project.title}</h3>
                    <p className="text-neutral-300 text-sm mb-4 font-extralight">{project.category}</p>
                    <div className="flex items-center text-white text-sm font-extralight tracking-wider">
                      <Link href={`/projects`}>VIEW PROJECT</Link>
                      <HiOutlineArrowNarrowRight className="ml-2" />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section
        id="services"
        ref={(el) => registerSectionRef('services', el)}
        className="py-20 px-4 md:px-8 bg-white"
      >
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible.services ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="chaumet-heading text-3xl md:text-4xl mb-4 text-neutral-900"
            >
              Our <span className="font-light">Services</span>
            </motion.h2>
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={isVisible.services ? { opacity: 1, width: '3rem' } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="chaumet-divider"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={isVisible.services ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                className="text-center p-6"
              >
                <h3 className="text-xl font-light mb-4 text-neutral-900">{service.title}</h3>
                <div className="w-10 h-px bg-neutral-300 mx-auto mb-6" />
                <p className="text-neutral-600 font-extralight leading-relaxed">{service.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section
        id="contact"
        ref={(el) => registerSectionRef('contact', el)}
        className="py-20 px-4 md:px-8 bg-neutral-900 text-white"
      >
        <div className="max-w-5xl mx-auto">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={isVisible.contact ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8 }}
                className="chaumet-heading text-3xl md:text-4xl mb-4 text-white"
              >
                Contact <span className="font-light">Us</span>
              </motion.h2>
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={isVisible.contact ? { opacity: 1, width: '3rem' } : {}}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="h-px bg-white/50 mb-8"
              />
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={isVisible.contact ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-neutral-300 mb-8 font-extralight leading-relaxed"
              >
                Let's collaborate to create stunning visualizations that communicate your design intent with clarity and impact.
              </motion.p>
              <motion.form
                initial={{ opacity: 0, y: 20 }}
                animate={isVisible.contact ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="space-y-4 max-w-md"
                onSubmit={handleContactSubmit}
              >
                <div>
                  <input
                    type="text"
                    name="name"
                    value={contactForm.name}
                    onChange={handleContactInputChange}
                    placeholder="Your Name"
                    required
                    className="w-full px-4 py-3 bg-transparent border border-white/20 text-white placeholder-white/50 focus:outline-none focus:border-white/50 font-extralight"
                  />
                </div>
                <div>
                  <input
                    type="email"
                    name="email"
                    value={contactForm.email}
                    onChange={handleContactInputChange}
                    placeholder="Your Email"
                    required
                    className="w-full px-4 py-3 bg-transparent border border-white/20 text-white placeholder-white/50 focus:outline-none focus:border-white/50 font-extralight"
                  />
                </div>
                <div>
                  <textarea
                    name="message"
                    value={contactForm.message}
                    onChange={handleContactInputChange}
                    placeholder="Your Message"
                    rows={4}
                    required
                    className="w-full px-4 py-3 bg-transparent border border-white/20 text-white placeholder-white/50 focus:outline-none focus:border-white/50 font-extralight resize-none"
                  ></textarea>
                </div>

                {/* Submit Message */}
                {submitMessage && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-4 rounded-lg border text-sm font-light ${
                      submitStatus === 'success'
                        ? 'bg-green-900/20 border-green-400/30 text-green-400'
                        : 'bg-red-900/20 border-red-400/30 text-red-400'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {submitStatus === 'success' ? (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      )}
                      <span>{submitMessage}</span>
                    </div>
                  </motion.div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`chaumet-button border-white text-white group ${
                    isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center gap-2">
                    {isSubmitting && (
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    {isSubmitting ? 'SENDING...' : 'SEND MESSAGE'}
                  </span>
                </button>
              </motion.form>
            </div>
            <div className="md:w-1/2 md:pl-12">
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={isVisible.contact ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="relative h-[400px] overflow-hidden"
                style={{
                  backgroundImage: `url(/hero/View_2-Driveway-Option-1.jpg)`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
                aria-label="Contact us"
              >
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Whatsapp Contact Section */}
      <WhatsAPPComponent/>
    </div>
  );
}
