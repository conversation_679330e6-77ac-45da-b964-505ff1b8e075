'use client'

import Link from 'next/link'
import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { IoCarOutline } from "react-icons/io5";
import { IoBedOutline } from "react-icons/io5";
import { TbArrowAutofitHeight } from "react-icons/tb";
import { SiLevelsdotfyi } from "react-icons/si";
import { LuBath } from "react-icons/lu";
import { TbArrowAutofitWidth } from "react-icons/tb";
import { MdClose } from 'react-icons/md';
import { HiDownload, HiEye, HiX } from 'react-icons/hi';

export default function TextWrapper({data = {}}) {
  const { data: session } = useSession()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    number: '',
    message: '',
  });
  const [loading, setLoading] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [showMessgaeInput,setShowMessgaeInput]=useState(false)
  const [showDownloadModal, setShowDownloadModal] = useState(false)
  const [downloadType, setDownloadType] = useState('')
  const [downloadLoading, setDownloadLoading] = useState(false)
  const [downloadError, setDownloadError] = useState('')
  const css = {
    icons:'w-5 h-5',
    title:'text-xs xl:text-sm capitalize font-medium',
    desc:'text-xs xl:text-sm capitalize font-medium',
    button:'flex items-center justify-center font-bold text-xs capitalize h-10 rounded w-full bg-slate-100 shadow',
    divContainer:'flex flex-col gap-0 text-xs w-fit h-fit justify-center items-center p-2',
    text:'flex w-full h-10 flex-none px-2 rounded shadow bg-slate-200 shadow-md placeholder:text-xs',
    textArea:'flex w-full rounded px-2 shadow bg-slate-200 shadow-md placeholder:text-xs',
  }
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const inputsFields=[
    {
      // onChange:handleChange,
      name:'name',type:'text',placeholder:'name'},
    // {
    //   // onChange:handleChange,
    //   name:'surname',type:'text',placeholder:'surname'},
    {
      // onChange:handleChange,
      name:'email',type:'email',placeholder:'email'},
    {
      // onChange:handleChange,
      name:'number',type:'tel',placeholder:'phone'},
  ]

  // Check if user has access to download files
  const hasDownloadAccess = () => {
    if (!session?.user) return false

    const userRole = session.user.role
    const userProjects = session.user.projects || []

    // Admin users can download from all buildings
    if (userRole === 'admin') return true

    // Client users can download from their assigned projects
    if (userRole === 'client' && data._id) {
      return userProjects.includes(data._id.toString())
    }

    // Regular users can download from buildings with collections
    return data.collections && data.collections.length > 0
  }

  const handleDownloadClick = (type) => {
    if (!hasDownloadAccess()) {
      setDownloadError('You need to be logged in and have access to download files from this project.')
      return
    }

    setDownloadType(type)
    setShowDownloadModal(true)
    setDownloadError('')
  }

  const downloadFile = async (file) => {
    try {
      setDownloadLoading(true)

      // Create a temporary link and trigger download
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.name
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

    } catch (error) {
      console.error('Download error:', error)
      setDownloadError('Failed to download file. Please try again.')
    } finally {
      setDownloadLoading(false)
    }
  }

  const getDownloadFiles = () => {
    if (downloadType === 'brochure') {
      return data.presentationDrawings || []
    } else if (downloadType === 'drawings') {
      return data.constructionDrawingsPdf || []
    }
    return []
  }

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters';
    }

    return errors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // console.log('TextWrapper form submitted:', formData);
    setSubmitMessage('');

    // Validate form
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setSubmitMessage(`Validation error: ${Object.values(errors)[0]}`);
      return;
    }

    setLoading(true);

    try {
      // Prepare data for the new contact API
      const contactData = {
        name: formData.name,
        email: formData.email,
        phone: formData.number,
        message: formData.message,
      };

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitMessage('Message sent successfully via email and WhatsApp!');
        setFormData({
          name: '',
          email: '',
          number: '',
          message: '',
        });
        // Close modal after successful submission
        setTimeout(() => {
          setShowMessgaeInput(false);
        }, 2000);
      } else {
        setSubmitMessage(`Failed to send message: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setSubmitMessage('An error occurred while sending your message.');
    } finally {
      setLoading(false);
    }
  };

  // console.log('TextWrapper:',data)
  return (
    <div className='textwrapper select-none flex relative flex-col gap-0 h-full lg:h-full overflow-hidden z-0'>
      <div className='flex text-gray-500 flex-col w-full h-fit gap-2 mb-11 px-4 md:mb-2 overflow-y-auto overflow-x-hidden'>
        {data?.buildingSummary && (
          <div className='flex sticky top-4 left-0 mt-4 rounded-md shadow-md items-center justify-around bg-slate-100 w-full h-20'>
            <div className={css.divContainer}>
              <p className={css.title}>length</p>
              <p className={css.desc}>{data.buildingSummary.length || '-'}</p>
              <TbArrowAutofitWidth className={css.icons} />
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>width</p>
              <p className={css.desc}>{data.buildingSummary.width || '-'}</p>
              <TbArrowAutofitHeight className={css.icons} />
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>beds</p>
              <p className={css.desc}>{data.buildingSummary.beds || '-'}</p>
              <IoBedOutline className={css.icons}/>
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>levels</p>
              <p className={css.desc}>{data.buildingSummary.levels || '-'}</p>
              <SiLevelsdotfyi className={css.icons}/>
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>baths</p>
              <p className={css.desc}>{data.buildingSummary.baths || '-'}</p>
              <LuBath className={css.icons}/>
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>cars</p>
              <p className={css.desc}>{data.buildingSummary.cars || '-'}</p>
              <IoCarOutline className={css.icons}/>
            </div>
          </div>
        )}
        <h1 className='md:text-5xl text-2xl font-bold capitalize h-fit md:my-3 tracking-tight leading-10'>{data?.buildingTitle || 'Building'}</h1>
        {data?.buildingType && (
          <Link href={`/buildings?collection=${data.buildingType}`} className='text-sm md:py-1 capitalize underline cursor-pointer'>
            {data.buildingType}
          </Link>
        )}
        {data?.desc && <p className='text-sm leading-5 text-justify font-light'>{data.desc}</p>}
        {data?.features && <p className='text-sm font-medium leading-5 text-justify'>{data.features}</p>}
        <div className='flex gap-2 w-full flex-col px-1'>
          <hr className='border-1 border-gray-300'/>
          <div className='flex flex-col gap-1 w-full'>
            {Array.isArray(data?.buildingHighlights) && data.buildingHighlights.map((i,index)=>
              (<div key={index} className='flex w-full flex-col'>
                <h1 className='text-xs font-semibold justify-center text-center underline'>{i?.title || ''}</h1>
                <p className='text-xs font-light justify-center text-center'>{i?.description || ''}</p>
              </div>)
            )}
          </div>
          <hr className='border-1 border-gray-300'/>
        </div>
        {data?.outroSection && <p className='text-sm font-light leading-5 text-justify'>{data.outroSection}</p>}
        <section id='contact' className='flex flex-col w-full h-fit rounded-md py-1 mb-1'>
          <div className='flex flex-col w-full h-fit gap-2 bg-slate-200 rounded-t-2xl p-2 shadow-md'>
            {/* Download Brochure Button */}
            <button
              onClick={() => handleDownloadClick('brochure')}
              disabled={!hasDownloadAccess() || !data.presentationDrawings?.length}
              className={`flex items-center justify-center gap-2 w-full min-h-12 rounded-lg shadow capitalize font-bold text-xs transition-colors ${
                hasDownloadAccess() && data.presentationDrawings?.length
                  ? 'bg-gray-100 hover:bg-gray-200 cursor-pointer text-gray-800'
                  : 'bg-gray-300 cursor-not-allowed text-gray-500'
              }`}
            >
              <HiDownload className="w-4 h-4" />
              Download Brochure
              {data.presentationDrawings?.length > 0 && (
                <span className="text-xs bg-gray-600 text-white px-2 py-1 rounded-full">
                  {data.presentationDrawings.length}
                </span>
              )}
            </button>

            {/* Download Drawings Button */}
            <button
              onClick={() => handleDownloadClick('drawings')}
              disabled={!hasDownloadAccess() || !data.constructionDrawingsPdf?.length}
              className={`flex items-center justify-center gap-2 w-full min-h-12 rounded-lg shadow capitalize font-bold text-xs transition-colors ${
                hasDownloadAccess() && data.constructionDrawingsPdf?.length
                  ? 'bg-gray-100 hover:bg-gray-200 cursor-pointer text-gray-800'
                  : 'bg-gray-300 cursor-not-allowed text-gray-500'
              }`}
            >
              <HiDownload className="w-4 h-4" />
              Download Drawings
              {data.constructionDrawingsPdf?.length > 0 && (
                <span className="text-xs bg-gray-600 text-white px-2 py-1 rounded-full">
                  {data.constructionDrawingsPdf.length}
                </span>
              )}
            </button>

            {/* Access Info */}
            {!hasDownloadAccess() && (
              <div className="text-xs text-gray-600 text-center p-2 bg-gray-100 rounded-lg">
                {!session?.user ? 'Sign in to download files' : 'Contact admin for download access'}
              </div>
            )}
          </div>
          <form
            className='flex flex-col w-full h-fit gap-2 mb-4 lg:mb-14 bg-slate-500 rounded-b-2xl p-2 shadow-md'
            // onSubmit={handleSubmit}
          >
            {inputsFields.map(i=>
              <input
                key={i?.name}
                className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
                {...i}
                onChange={(e)=>handleChange(e)}
              />
            )}
            <textarea
              placeholder='message'
              name='message'
              className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
              rows={5}
              onChange={(e)=>handleChange(e)}
            />
            <input onClick={handleSubmit} type="button" value="send message" className='flex items-center justify-center w-full min-h-10 rounded-md bg-slate-800 cursor-pointer outline-none hover:bg-slate-700 duration-300 ease-linear text-sm capitalize text-white'/>
          </form>
        </section>
      </div>
      <div className='flex absolute bottom-0 bg-gray-50 w-full h-fit p-2 rounded-lg shadow-md'>
        <button
          type="button"
          onClick={() => setShowMessgaeInput(true)}
          className='flex items-center text-white capitalize justify-center rounded-md bg-gray-800 w-full min-h-12'
        >
          contact
        </button>
      </div>
      {/* Message Input Modal */}
      {
        showMessgaeInput && <div className='flex absolute m-auto z-20 w-full h-full bg-black/75 items-center justify-center p-4'>
          <form
            className='flex relative flex-col w-full h-fit gap-2 mb-14 bg-slate-500 rounded-b-2xl p-2 shadow-md'
            onSubmit={handleSubmit}
          >
            <MdClose
              type="button"
              onClick={() => setShowMessgaeInput(false)}
              className='flex absolute -top-14 right-2 items-center text-white capitalize justify-center rounded-full bg-gray-800 w-fit min-h-12 p-1'
            />
            {inputsFields.map(i=>
              <input
                key={i?.name}
                className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
                {...i}
                value={formData[i.name] || ''}
                onChange={(e)=>handleChange(e)}
                required
              />
            )}
            <textarea
              placeholder='message'
              name='message'
              value={formData.message || ''}
              className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
              rows={5}
              onChange={(e)=>handleChange(e)}
              required
            />

            {/* Submit Message */}
            {submitMessage && (
              <div className={`text-sm p-3 rounded-lg border flex items-center gap-2 ${
                submitMessage.includes('successfully')
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                {submitMessage.includes('successfully') ? (
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                )}
                <span>{submitMessage}</span>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className={`flex items-center justify-center gap-2 w-full min-h-10 rounded-md bg-slate-800 cursor-pointer outline-none hover:bg-slate-700 duration-300 ease-linear text-sm capitalize text-white ${
                loading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {loading && (
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              {loading ? 'Sending...' : 'Submit'}
            </button>
          </form>
        </div>
      }

      {/* Download Modal */}
      {showDownloadModal && (
        <div className='flex absolute m-auto z-30 w-full h-full bg-black/75 items-center justify-center p-4'>
          <div className='flex relative flex-col w-full max-w-md h-fit gap-4 bg-white rounded-lg p-6 shadow-lg'>
            <button
              onClick={() => setShowDownloadModal(false)}
              className='flex absolute -top-3 -right-3 items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 w-8 h-8 text-white transition-colors'
            >
              <HiX className="w-4 h-4" />
            </button>

            <div className='flex flex-col gap-2'>
              <h3 className='text-lg font-bold text-gray-800 capitalize'>
                Download {downloadType === 'brochure' ? 'Presentation Brochures' : 'Construction Drawings'}
              </h3>

              {downloadError && (
                <div className="text-red-600 text-sm p-2 bg-red-50 rounded">
                  {downloadError}
                </div>
              )}

              <div className='flex flex-col gap-2 max-h-64 overflow-y-auto'>
                {getDownloadFiles().map((file, index) => (
                  <div key={file.id || index} className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                    <div className='flex flex-col flex-1'>
                      <span className='text-sm font-medium text-gray-800'>{file.name}</span>
                      {file.size && (
                        <span className='text-xs text-gray-500'>
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                      )}
                    </div>
                    <div className='flex gap-2'>
                      <button
                        onClick={() => window.open(file.url, '_blank')}
                        className='flex items-center justify-center w-8 h-8 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded transition-colors'
                        title="Preview"
                      >
                        <HiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => downloadFile(file)}
                        disabled={downloadLoading}
                        className='flex items-center justify-center w-8 h-8 bg-green-100 hover:bg-green-200 text-green-600 rounded transition-colors disabled:opacity-50'
                        title="Download"
                      >
                        <HiDownload className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {getDownloadFiles().length === 0 && (
                <div className="text-center text-gray-500 py-4">
                  No {downloadType === 'brochure' ? 'presentation files' : 'construction drawings'} available
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
