'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { FiAlertTriangle, FiArrowLeft, FiRefreshCw } from 'react-icons/fi'
import Link from 'next/link'

export default function Error({ error, reset }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Users management page error:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          {/* Error Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
            <FiAlertTriangle className="h-8 w-8 text-red-600" />
          </div>

          {/* Error Message */}
          <h1 className="chaumet-heading text-2xl text-neutral-900 mb-4">
            Unable to load users
          </h1>
          
          <p className="text-neutral-600 font-light mb-8 leading-relaxed">
            We encountered an error while loading the user management interface. This could be due to a network issue or server problem.
          </p>

          {/* Error Details (in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
              <h3 className="text-sm font-medium text-red-800 mb-2">Error Details:</h3>
              <pre className="text-xs text-red-700 whitespace-pre-wrap break-words">
                {error?.message || 'Unknown error occurred'}
              </pre>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={reset}
              className="chaumet-button border-neutral-900 text-neutral-900 group"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center">
                <FiRefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </span>
            </button>
            
            <Link
              href="/admin"
              className="chaumet-button border-neutral-300 text-neutral-600 hover:border-neutral-900 hover:text-neutral-900"
            >
              <FiArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </div>

          {/* Additional Help */}
          <div className="mt-8 pt-6 border-t border-neutral-200">
            <p className="text-sm text-neutral-500 font-light">
              If this problem persists, please contact your system administrator.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
