import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import UserService from '@/libs/userSchema'

// GET /api/admin/users - Get all users with pagination and search
export async function GET(request) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page')) || 1
    const limit = parseInt(searchParams.get('limit')) || 10
    const search = searchParams.get('search') || ''
    const sortBy = searchParams.get('sortBy') || 'dateCreated'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const result = await UserService.getAllUsers(page, limit, search, sortBy, sortOrder)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in GET /api/admin/users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// POST /api/admin/users - Create new user
export async function POST(request) {
  try {
    const session = await auth()

    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { email, firstName, lastName, phone, role } = body

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate role
    const validRoles = ['admin', 'client']
    const userRole = role || 'client'
    if (!validRoles.includes(userRole)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be admin or client' },
        { status: 400 }
      )
    }

    // Create user using UserService
    const newUser = await UserService.createUser({
      email,
      firstName: firstName || '',
      lastName: lastName || '',
      phone: phone || null,
      role: userRole,
      status: 'pending'
    })

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: newUser
    })
  } catch (error) {
    console.error('Error in POST /api/admin/users:', error)

    if (error.message === 'User already exists') {
      return NextResponse.json(
        { error: 'A user with this email already exists' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/users - Bulk delete users
export async function DELETE(request) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userIds } = body

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: 'User IDs array is required' },
        { status: 400 }
      )
    }

    // Prevent admin from deleting themselves
    if (userIds.includes(session.user.id)) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    const result = await UserService.deleteMultipleUsers(userIds)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in DELETE /api/admin/users:', error)
    return NextResponse.json(
      { error: 'Failed to delete users' },
      { status: 500 }
    )
  }
}
