'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { FiUsers, FiArrowLeft } from 'react-icons/fi'
import Link from 'next/link'
import UsersManagement from '@/components/admin/UsersManagement'

export default function UsersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user?.role !== 'admin') {
      router.push('/auth/signin')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-600"></div>
      </div>
    )
  }

  if (!session || session.user?.role !== 'admin') {
    return null
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm font-light text-neutral-600 mb-4">
            <Link
              href="/admin"
              className="hover:text-neutral-900 transition-colors duration-300"
            >
              Admin Dashboard
            </Link>
            <span>/</span>
            <span className="text-neutral-900">Users</span>
          </nav>

          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin"
                className="chaumet-button border-neutral-300 text-neutral-600 group hover:border-neutral-900 hover:text-neutral-900"
              >
                <FiArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
              <div>
                <h1 className="chaumet-heading text-3xl md:text-4xl text-neutral-900 mb-2">
                  User Management
                </h1>
                <p className="text-neutral-600 font-light tracking-wide">
                  Manage user accounts, roles, and project assignments
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-2 text-neutral-600">
              <FiUsers className="h-6 w-6" />
              <span className="font-light">Admin Panel</span>
            </div>
          </div>
        </motion.div>

        {/* Users Management Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-lg border border-neutral-200 shadow-sm"
        >
          <div className="p-6">
            <UsersManagement />
          </div>
        </motion.div>
      </div>
    </div>
  )
}
