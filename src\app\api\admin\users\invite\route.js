// Force Node.js runtime for email functionality
export const runtime = 'nodejs'

import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import UserService from '@/libs/userSchema'
import { ObjectId } from 'mongodb'

// POST /api/admin/users/invite - Send invitation email to user
export async function POST(request) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId, email } = body
    
    // Validate required fields
    if (!userId && !email) {
      return NextResponse.json(
        { error: 'Either userId or email is required' },
        { status: 400 }
      )
    }

    let user
    if (userId) {
      // Get user by ID
      if (!ObjectId.isValid(userId)) {
        return NextResponse.json(
          { error: 'Invalid user ID' },
          { status: 400 }
        )
      }
      user = await UserService.getUserById(new ObjectId(userId))
    } else {
      // Get user by email
      user = await UserService.getUserByEmail(email)
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Create invitation email content
    const baseUrl = process.env.AUTH_URL || process.env.NEXTAUTH_URL || 'https://localhost:3002'
    const inviteUrl = `${baseUrl}/auth/signin?email=${encodeURIComponent(user.email)}`
    
    const emailContent = {
      to: user.email,
      subject: 'Welcome to Luyari - Complete Your Account Setup',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Luyari</title>
          <style>
            body { 
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
              margin: 0; 
              padding: 0; 
              background-color: #f8fafc; 
              line-height: 1.6;
            }
            .container { 
              max-width: 600px; 
              margin: 0 auto; 
              background-color: white; 
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header { 
              background: linear-gradient(135deg, #1e293b 0%, #334155 100%); 
              padding: 40px 30px; 
              text-align: center; 
            }
            .header h1 { 
              color: white; 
              margin: 0; 
              font-size: 32px; 
              font-weight: 300; 
              letter-spacing: 2px; 
            }
            .header p {
              color: #cbd5e1;
              margin: 10px 0 0 0;
              font-weight: 300;
              letter-spacing: 0.5px;
              font-size: 16px;
            }
            .content { 
              padding: 40px 30px; 
            }
            .content h2 {
              color: #1e293b;
              font-size: 24px;
              font-weight: 400;
              margin: 0 0 20px 0;
            }
            .content p {
              color: #475569;
              font-size: 16px;
              margin: 0 0 16px 0;
            }
            .cta-button {
              display: inline-block;
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              color: white;
              padding: 16px 32px;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 16px;
              letter-spacing: 0.5px;
              transition: transform 0.2s ease;
            }
            .cta-button:hover {
              transform: translateY(-2px);
            }
            .divider {
              height: 1px;
              background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
              margin: 30px 0;
            }
            .footer {
              background-color: #f8fafc;
              padding: 30px;
              text-align: center;
              color: #64748b;
              font-size: 14px;
            }
            .footer p {
              margin: 5px 0;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>luyari.</h1>
              <p>Architectural Visualization</p>
            </div>
            
            <div class="content">
              <h2>Welcome to Luyari!</h2>
              
              <p>Hello${user.firstName ? ` ${user.firstName}` : ''},</p>
              
              <p>You've been invited to join the Luyari platform by one of our administrators. We're excited to have you as part of our architectural visualization community.</p>
              
              <p>Luyari specializes in creating photorealistic architectural visualizations that help architects, designers, and developers communicate their vision with clarity and impact.</p>
              
              <p>To complete your account setup and start exploring our platform, please click the button below:</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${inviteUrl}" class="cta-button">Complete Account Setup</a>
              </div>
              
              <p style="font-size: 14px; color: #94a3b8;">This invitation link will allow you to sign in and verify your account. If you have any questions, please contact our support team.</p>
              
              <div class="divider"></div>
              
              <p style="font-size: 14px; color: #94a3b8;">
                If the button above doesn't work, you can copy and paste this link into your browser:<br>
                <a href="${inviteUrl}" style="color: #3b82f6; word-break: break-all;">${inviteUrl}</a>
              </p>
            </div>
            
            <div class="footer">
              <p>© ${new Date().getFullYear()} Luyari Architectural Visualization. All rights reserved.</p>
              <p>This email was sent because an administrator invited you to join our platform.</p>
            </div>
          </div>
        </body>
        </html>
      `
    }

    // Send email using the existing email API
    const baseUrl = process.env.AUTH_URL || process.env.NEXTAUTH_URL || 'https://localhost:3002'
    const emailResponse = await fetch(`${baseUrl}/api/send-email`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(emailContent)
    })

    if (!emailResponse.ok) {
      const errorData = await emailResponse.json()
      throw new Error(`Failed to send email: ${errorData.error || 'Unknown error'}`)
    }

    // Update user status to 'invited'
    await UserService.updateUser(new ObjectId(user.id), { 
      status: 'invited',
      invitedAt: new Date(),
      invitedBy: session.user.id
    })

    return NextResponse.json({
      success: true,
      message: 'Invitation sent successfully',
      sentTo: user.email,
      sentAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in POST /api/admin/users/invite:', error)
    
    if (error.message === 'User not found') {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to send invitation' },
      { status: 500 }
    )
  }
}
