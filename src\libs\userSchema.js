import clientPromise from './mongodb'

/**
 * User Schema Definition for MongoDB
 *
 * Fields:
 * - username: string (display name)
 * - email: string (unique identifier)
 * - firstName: string (user's first name)
 * - lastName: string (user's last name)
 * - phone: string (contact number)
 * - website: string (optional website URL)
 * - projects: array (list of assigned project IDs)
 * - role: string (user role: 'admin', 'user', or 'client')
 * - dateCreated: date (registration timestamp)
 */

export class UserService {
  static async getDatabase() {
    const client = await clientPromise
    return client.db()
  }

  static async getAllUsers(page = 1, limit = 10, search = '', sortBy = 'dateCreated', sortOrder = 'desc') {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')
      
      // Build search query
      let query = {}
      if (search) {
        query = {
          $or: [
            { username: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } }
          ]
        }
      }

      // Build sort object
      const sort = {}
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1

      // Calculate pagination
      const skip = (page - 1) * limit

      // Get users with pagination
      const users = await collection
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .toArray()

      // Get total count for pagination
      const total = await collection.countDocuments(query)

      return {
        users: users.map(user => ({
          id: user._id.toString(),
          username: user.username || user.name || 'Unknown',
          email: user.email,
          phone: user.phone || null,
          projects: user.projects || [],
          role: user.role || 'client',
          dateCreated: user.dateCreated || new Date()
        })),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      throw new Error('Failed to fetch users')
    }
  }

  static async getUserById(userId) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')

      const user = await collection.findOne({ _id: userId })

      if (!user) {
        throw new Error('User not found')
      }

      return {
        id: user._id.toString(),
        username: user.username || user.name || 'Unknown',
        email: user.email,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || null,
        website: user.website || '',
        projects: user.projects || [],
        role: user.role || 'client',
        dateCreated: user.dateCreated || new Date()
      }
    } catch (error) {
      console.error('Error fetching user:', error)
      throw new Error('Failed to fetch user')
    }
  }

  static async getUserByEmail(email) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')

      const user = await collection.findOne({ email: email })

      if (!user) {
        throw new Error('User not found')
      }

      return {
        id: user._id.toString(),
        username: user.username || user.name || 'Unknown',
        email: user.email,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || null,
        website: user.website || '',
        projects: user.projects || [],
        role: user.role || 'client',
        dateCreated: user.dateCreated || new Date()
      }
    } catch (error) {
      console.error('Error fetching user by email:', error)
      throw new Error('Failed to fetch user')
    }
  }

  static async updateUserByEmail(email, updateData) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')

      const result = await collection.updateOne(
        { email: email },
        {
          $set: {
            ...updateData,
            updatedAt: new Date()
          }
        }
      )

      if (result.matchedCount === 0) {
        throw new Error('User not found')
      }

      // Return updated user
      const updatedUser = await collection.findOne({ email: email })
      return {
        id: updatedUser._id.toString(),
        username: updatedUser.username || updatedUser.name || 'Unknown',
        email: updatedUser.email,
        firstName: updatedUser.firstName || '',
        lastName: updatedUser.lastName || '',
        phone: updatedUser.phone || null,
        website: updatedUser.website || '',
        projects: updatedUser.projects || [],
        role: updatedUser.role || 'client',
        dateCreated: updatedUser.dateCreated || new Date()
      }
    } catch (error) {
      console.error('Error updating user:', error)
      throw new Error('Failed to update user')
    }
  }

  static async updateUser(userId, updates, adminUserId = null) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')

      // Check if projects are being updated and validate admin permissions
      if (updates.projects !== undefined) {
        if (!adminUserId) {
          throw new Error('Admin authorization required to modify projects')
        }

        // Verify the admin user exists and has admin role
        const adminUser = await collection.findOne({ _id: adminUserId })
        if (!adminUser || adminUser.role !== 'admin') {
          throw new Error('Only admin users can modify project assignments')
        }

        // Ensure projects are only assigned to client users
        const targetUser = await collection.findOne({ _id: userId })
        if (!targetUser) {
          throw new Error('Target user not found')
        }

        if (targetUser.role !== 'client' && updates.projects.length > 0) {
          throw new Error('Projects can only be assigned to client users')
        }
      }

      // Only allow specific fields to be updated
      const allowedUpdates = {
        ...(updates.role && { role: updates.role }),
        ...(updates.phone !== undefined && { phone: updates.phone }),
        ...(updates.projects !== undefined && { projects: updates.projects }),
        ...(updates.username && { username: updates.username }),
        ...(updates.firstName !== undefined && { firstName: updates.firstName }),
        ...(updates.lastName !== undefined && { lastName: updates.lastName }),
        ...(updates.website !== undefined && { website: updates.website })
      }

      const result = await collection.updateOne(
        { _id: userId },
        { $set: { ...allowedUpdates, updatedAt: new Date() } }
      )

      if (result.matchedCount === 0) {
        throw new Error('User not found')
      }

      return await this.getUserById(userId)
    } catch (error) {
      console.error('Error updating user:', error)
      throw new Error('Failed to update user')
    }
  }

  static async deleteUser(userId) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')
      
      const result = await collection.deleteOne({ _id: userId })
      
      if (result.deletedCount === 0) {
        throw new Error('User not found')
      }

      return { success: true, message: 'User deleted successfully' }
    } catch (error) {
      console.error('Error deleting user:', error)
      throw new Error('Failed to delete user')
    }
  }

  static async deleteMultipleUsers(userIds) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')
      
      const result = await collection.deleteMany({
        _id: { $in: userIds }
      })

      return {
        success: true,
        message: `${result.deletedCount} users deleted successfully`,
        deletedCount: result.deletedCount
      }
    } catch (error) {
      console.error('Error deleting multiple users:', error)
      throw new Error('Failed to delete users')
    }
  }

  static async assignProjectsToUser(userId, projectIds) {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')
      
      const result = await collection.updateOne(
        { _id: userId },
        { $set: { projects: projectIds } }
      )

      if (result.matchedCount === 0) {
        throw new Error('User not found')
      }

      return await this.getUserById(userId)
    } catch (error) {
      console.error('Error assigning projects:', error)
      throw new Error('Failed to assign projects')
    }
  }

  static async getUserStats() {
    try {
      const db = await this.getDatabase()
      const collection = db.collection('users')
      
      const totalUsers = await collection.countDocuments()
      const adminUsers = await collection.countDocuments({ role: 'admin' })
      const regularUsers = await collection.countDocuments({ role: 'user' })
      const clientUsers = await collection.countDocuments({ role: 'client' })

      // Get recent registrations (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentUsers = await collection.countDocuments({
        dateCreated: { $gte: thirtyDaysAgo }
      })

      return {
        total: totalUsers,
        admins: adminUsers,
        users: regularUsers,
        clients: clientUsers,
        recentRegistrations: recentUsers
      }
    } catch (error) {
      console.error('Error fetching user stats:', error)
      throw new Error('Failed to fetch user statistics')
    }
  }
}

export default UserService
