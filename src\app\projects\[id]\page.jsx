import BuildPageComponent from '@/components/BuildingPage/BuildPageComponent'
import ScrollerWrapper from '@/components/BuildingPage/ScrollerWrapper'
import ExperienceWrapper from '@/components/experience/ExperienceWrapper'
import Image from 'next/image'
import LoadingComponent from '@/components/LoadingComponent'
import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { auth } from '@/auth'
import dbConnect from '@/libs/mongoDb/connectToLuyariDB'
import { Building } from '@/libs/mongoDb/models/Building'
import mongoose from 'mongoose'

export default async function page({params}) {
    const cssSection='sectionWrapper flex w-full h-full flex-none relative overflow-hidden'
    const {id}=await params

    try {
        // Get session server-side
        const session = await auth()

        // Connect to database
        await dbConnect()

        // Validate ObjectId
        if (!mongoose.Types.ObjectId.isValid(id)) {
            notFound()
        }

        const building = await Building.findById(id).lean()

        if (!building) {
            notFound()
        }

        // Apply access control
        if (session?.user) {
            const userRole = session.user.role
            const userProjects = session.user.projects || []

            if (userRole === 'admin') {
                // Admin users can access all buildings
            } else if (userRole === 'client') {
                // Client users can only access:
                // 1. Buildings with buildingRole "client" that are in their projects array
                // 2. Buildings with entries in collections property
                const hasAccess =
                    (building.buildingRole === 'client' && userProjects.includes(building._id.toString())) ||
                    (building.collections && building.collections.length > 0)

                if (!hasAccess) {
                    return <AccessDeniedPage />
                }
            } else {
                // Regular users can only access buildings with collections entries
                if (!building.collections || building.collections.length === 0) {
                    return <AccessDeniedPage />
                }
            }
        } else {
            // Unauthenticated users can only access buildings with collections entries
            if (!building.collections || building.collections.length === 0) {
                return <AccessDeniedPage />
            }
        }

        const data = building
        console.log('project page', data)

        return (
            <BuildPageComponent data={building}>
                {/* renders wrapper */}
                <section className={cssSection}>
                  <ScrollerWrapper>
                    {building?.renders?.map((i,index)=>
                      <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                        <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
                      </div>
                    )}
                  </ScrollerWrapper>
                </section>

                {/* drawings wrapper */}
                <section className={cssSection}>
                  <ScrollerWrapper>
                    {building?.drawings?.map((i,index)=>
                      <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                        <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
                      </div>
                    )}
                  </ScrollerWrapper>
                </section>

                {/* experience wrapper */}
                <section className={cssSection}>
                  <Suspense fallback={<LoadingComponent/>}>
                    <ExperienceWrapper data={building}/>
                  </Suspense>
                </section>
            </BuildPageComponent>
        )
    } catch (error) {
        console.error('Error loading project:', error)
        return <ErrorPage error={error.message} />
    }
}

// Access Denied Component
function AccessDeniedPage() {
    return (
        <div className="min-h-screen bg-neutral-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white border border-neutral-200 rounded-lg p-8 text-center shadow-sm">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <h1 className="chaumet-heading text-2xl text-neutral-900 mb-4">Access Denied</h1>
                <p className="text-neutral-600 font-light mb-6">
                    You don't have permission to view this project. Please contact your administrator if you believe this is an error.
                </p>
                <Link
                    href="/profile"
                    className="inline-block bg-neutral-900 text-white px-6 py-2 rounded-lg hover:bg-neutral-800 transition-colors font-light"
                >
                    Return to Profile
                </Link>
            </div>
        </div>
    )
}

// Error Page Component
function ErrorPage({ error }) {
    return (
        <div className="min-h-screen bg-neutral-50 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white border border-neutral-200 rounded-lg p-8 text-center shadow-sm">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h1 className="chaumet-heading text-2xl text-neutral-900 mb-4">Something went wrong</h1>
                <p className="text-neutral-600 font-light mb-2">
                    We encountered an error while loading this project.
                </p>
                <p className="text-sm text-neutral-500 font-light mb-6">
                    {error}
                </p>
                <div className="space-y-2">
                    <Link
                        href="/projects"
                        className="block bg-neutral-900 text-white px-6 py-2 rounded-lg hover:bg-neutral-800 transition-colors font-light"
                    >
                        View All Projects
                    </Link>
                    <Link
                        href="/profile"
                        className="block text-neutral-600 hover:text-neutral-900 transition-colors font-light"
                    >
                        Return to Profile
                    </Link>
                </div>
            </div>
        </div>
    )
}
