# Project Assignment Feature Testing Guide

## Overview
This guide outlines how to test the newly implemented project assignment feature for client role users.

## Prerequisites
1. Admin user account with access to `/admin` pages
2. At least one client user in the system
3. At least one building with `buildingRole: 'client'` in the database

## Testing Steps

### 1. Test API Endpoint for Assignable Buildings
**Endpoint**: `GET /api/admin/buildings/assignable`

**Test Cases**:
- Access as admin user (should work)
- Access as non-admin user (should return 401)
- Search functionality with query parameter
- Verify only buildings with `buildingRole: 'client'` are returned

**Expected Response**:
```json
{
  "buildings": [
    {
      "id": "building_id",
      "value": "building_id", 
      "label": "Project Title - Building Title",
      "projectTitle": "Project Title",
      "buildingTitle": "Building Title",
      "buildingType": "multi-storey",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "total": 1
}
```

### 2. Test User Management Interface
**Location**: `/admin` → Users Management

**Test Cases**:
1. **View Projects Column**:
   - Navigate to Users Management
   - Verify "Projects" column is visible in the table
   - Check that non-client users show "Not applicable"
   - Check that client users show project assignment interface

2. **Edit Mode for Client Users**:
   - Click edit button for a client user
   - Verify ProjectAssignment component becomes interactive
   - Check search input appears
   - Verify dropdown functionality

3. **Project Assignment**:
   - Search for available projects in the dropdown
   - Select a project from the dropdown
   - Verify project is added to the user's assigned projects
   - Check visual feedback (success message, project badge)
   - Verify duplicate prevention (same project can't be added twice)

4. **Project Removal**:
   - Click the X button on an assigned project badge
   - Verify project is removed from the user's assignments
   - Check visual feedback for removal

### 3. Test API Integration
**Endpoint**: `PUT /api/admin/users/[id]`

**Test Cases**:
1. **Valid Project Assignment**:
   ```json
   {
     "projects": ["building_id_1", "building_id_2"]
   }
   ```
   - Should succeed for client users
   - Should update user's projects array in database

2. **Invalid Project Assignment**:
   ```json
   {
     "projects": ["building_id_1"]
   }
   ```
   - Should fail for non-client users with appropriate error message
   - Should validate projects array format

3. **Role Change Validation**:
   - Try assigning projects to a user while changing role to 'user'
   - Should fail with validation error

### 4. Test Profile Page Reflection
**Location**: `/profile` (as client user)

**Test Cases**:
1. Login as a client user who has been assigned projects
2. Navigate to profile page
3. Verify assigned projects are visible in the user's profile
4. Check that project information is correctly displayed

### 5. Test Building Access Control
**Test Cases**:
1. **Client User Access**:
   - Login as client user with assigned projects
   - Navigate to assigned building pages
   - Verify access is granted to assigned buildings
   - Verify download functionality works for assigned projects

2. **Access Restriction**:
   - Try accessing buildings not in the client's projects array
   - Verify access is denied appropriately

### 6. Test Error Handling
**Test Cases**:
1. **Network Errors**:
   - Simulate network failure during project assignment
   - Verify error messages are displayed
   - Check that local state reverts on error

2. **Invalid Building IDs**:
   - Try assigning non-existent building IDs
   - Verify appropriate error handling

3. **Permission Errors**:
   - Try accessing admin endpoints as non-admin user
   - Verify 401 responses are handled correctly

## Expected Behaviors

### Visual Feedback
- ✅ Success messages appear when projects are added/removed
- ✅ Loading indicators show during API calls
- ✅ Error messages display for failed operations
- ✅ Recently added/removed projects have visual highlighting
- ✅ Project count displays correctly

### Data Persistence
- ✅ Project assignments persist to MongoDB immediately
- ✅ Changes reflect in user management interface
- ✅ Profile page shows updated project assignments
- ✅ Building access control respects new assignments

### User Experience
- ✅ Dropdown search works smoothly
- ✅ Duplicate prevention works correctly
- ✅ Interface is responsive and intuitive
- ✅ Chaumet design system styling is consistent
- ✅ No page refreshes required for updates

## Common Issues to Check

1. **Import Errors**: Verify all component imports are correct
2. **API Route Issues**: Check that API endpoints are accessible
3. **Database Connection**: Ensure MongoDB connection is working
4. **Authentication**: Verify admin authentication is working
5. **Role Validation**: Check that role-based restrictions work
6. **State Management**: Ensure local state updates correctly

## Success Criteria
- [ ] All API endpoints respond correctly
- [ ] Project assignment interface works smoothly
- [ ] Visual feedback provides clear user guidance
- [ ] Data persists correctly to database
- [ ] Profile page reflects assignments
- [ ] Building access control works with new assignments
- [ ] Error handling is robust and user-friendly
