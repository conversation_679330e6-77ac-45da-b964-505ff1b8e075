import { NextResponse } from "next/server";
import { auth } from "@/auth";
import dbConnect from "@/libs/mongoDb/connectToLuyariDB";
import { Building } from "@/libs/mongoDb/models/Building";

/**
 * GET /api/buildings/collections - Get unique collections for filtering
 * Returns all unique collection values from buildings that the user has access to
 */
export async function GET(request) {
  try {
    const session = await auth();
    
    await dbConnect();

    // Build access control query
    let accessQuery = {};
    
    if (session?.user) {
      const userRole = session.user.role;
      const userProjects = session.user.projects || [];

      if (userRole === 'admin') {
        // Admin users can see collections from all buildings
        accessQuery = {};
      } else if (userRole === 'client') {
        // Client users can see collections from:
        // 1. Buildings with buildingRole "client" that are in their projects array
        // 2. Buildings with entries in collections property
        accessQuery = {
          $or: [
            {
              buildingRole: 'client',
              _id: { $in: userProjects.map(id => id) }
            },
            {
              collections: { $exists: true, $ne: [] }
            }
          ]
        };
      } else {
        // Regular users can see collections from buildings with collections entries
        accessQuery = {
          collections: { $exists: true, $ne: [] }
        };
      }
    } else {
      // Unauthenticated users can see collections from buildings with collections entries
      accessQuery = {
        collections: { $exists: true, $ne: [] }
      };
    }

    // Get unique collections using aggregation
    const collectionsAggregation = await Building.aggregate([
      { $match: accessQuery },
      { $unwind: "$collections" },
      { $group: { _id: "$collections" } },
      { $sort: { _id: 1 } }
    ]);

    const collections = collectionsAggregation.map(item => item._id).filter(Boolean);

    // Also get unique building types for additional filtering
    const buildingTypesAggregation = await Building.aggregate([
      { $match: accessQuery },
      { $group: { _id: "$buildingType" } },
      { $sort: { _id: 1 } }
    ]);

    const buildingTypes = buildingTypesAggregation.map(item => item._id).filter(Boolean);

    // Get summary level ranges for filtering
    const summaryRanges = await Building.aggregate([
      { $match: accessQuery },
      {
        $group: {
          _id: null,
          minBeds: { $min: "$buildingSummary.beds" },
          maxBeds: { $max: "$buildingSummary.beds" },
          minBaths: { $min: "$buildingSummary.baths" },
          maxBaths: { $max: "$buildingSummary.baths" },
          minCars: { $min: "$buildingSummary.cars" },
          maxCars: { $max: "$buildingSummary.cars" },
          minLevels: { $min: "$buildingSummary.levels" },
          maxLevels: { $max: "$buildingSummary.levels" }
        }
      }
    ]);

    const ranges = summaryRanges.length > 0 ? summaryRanges[0] : {
      minBeds: 0, maxBeds: 0,
      minBaths: 0, maxBaths: 0,
      minCars: 0, maxCars: 0,
      minLevels: 0, maxLevels: 0
    };

    return NextResponse.json({
      collections,
      buildingTypes,
      summaryRanges: {
        beds: { min: ranges.minBeds || 0, max: ranges.maxBeds || 0 },
        baths: { min: ranges.minBaths || 0, max: ranges.maxBaths || 0 },
        cars: { min: ranges.minCars || 0, max: ranges.maxCars || 0 },
        levels: { min: ranges.minLevels || 0, max: ranges.maxLevels || 0 }
      }
    });

  } catch (error) {
    console.error("Error fetching collections:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}
