'use client'

import React, { useState, useEffect } from 'react'
import {
  FiSearch,
  FiEye,
  FiTrash2,
  FiUsers,
  FiUserCheck,
  FiUserPlus,
  FiChevronLeft,
  FiChevronRight,
  FiMoreVertical,
  FiExternalLink
} from 'react-icons/fi'
import Link from 'next/link'

export default function UsersManagement() {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUsers, setSelectedUsers] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('dateCreated')
  const [sortOrder, setSortOrder] = useState('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({})
  const [stats, setStats] = useState({})
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState(null)

  const itemsPerPage = 10

  // Fetch users data
  const fetchUsers = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        search: searchTerm,
        sortBy,
        sortOrder
      })

      const response = await fetch(`/api/admin/users?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const data = await response.json()
      setUsers(data.users)
      setPagination(data.pagination)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Fetch user statistics
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/users/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (err) {
      console.error('Failed to fetch stats:', err)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [currentPage, searchTerm, sortBy, sortOrder])

  useEffect(() => {
    fetchStats()
  }, [])

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }

  // Handle sorting
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
    setCurrentPage(1)
  }

  // Handle user selection
  const handleSelectUser = (userId) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(users.map(user => user.id))
    }
  }

  // Handle user update
  const handleUpdateUser = async (userId, updates) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      if (!response.ok) {
        throw new Error('Failed to update user')
      }

      await fetchUsers()
    } catch (err) {
      setError(err.message)
    }
  }

  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete user')
      }

      await fetchUsers()
      await fetchStats()
      setShowDeleteConfirm(false)
      setDeleteTarget(null)
    } catch (err) {
      setError(err.message)
    }
  }

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    try {
      const response = await fetch('/api/admin/users', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userIds: selectedUsers }),
      })

      if (!response.ok) {
        throw new Error('Failed to delete users')
      }

      await fetchUsers()
      await fetchStats()
      setSelectedUsers([])
      setShowDeleteConfirm(false)
    } catch (err) {
      setError(err.message)
    }
  }

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading && users.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-white p-4 rounded-lg border border-neutral-200">
          <div className="flex items-center">
            <FiUsers className="h-8 w-8 text-neutral-600" />
            <div className="ml-3">
              <p className="text-sm font-light text-neutral-600">Total Users</p>
              <p className="text-2xl chaumet-heading text-neutral-900">{stats.total || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-neutral-200">
          <div className="flex items-center">
            <FiUserCheck className="h-8 w-8 text-neutral-600" />
            <div className="ml-3">
              <p className="text-sm font-light text-neutral-600">Admin Users</p>
              <p className="text-2xl chaumet-heading text-neutral-900">{stats.admins || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-neutral-200">
          <div className="flex items-center">
            <FiUserPlus className="h-8 w-8 text-neutral-600" />
            <div className="ml-3">
              <p className="text-sm font-light text-neutral-600">Client Users</p>
              <p className="text-2xl chaumet-heading text-neutral-900">{stats.clients || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-neutral-200">
          <div className="flex items-center">
            <FiUserPlus className="h-8 w-8 text-neutral-600" />
            <div className="ml-3">
              <p className="text-sm font-light text-neutral-600">Regular Users</p>
              <p className="text-2xl chaumet-heading text-neutral-900">{stats.users || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-neutral-200">
          <div className="flex items-center">
            <FiUserPlus className="h-8 w-8 text-neutral-600" />
            <div className="ml-3">
              <p className="text-sm font-light text-neutral-600">Recent (30d)</p>
              <p className="text-2xl chaumet-heading text-neutral-900">{stats.recentRegistrations || 0}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="bg-white p-4 rounded-lg border border-neutral-200">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search users by name, email, or phone..."
              value={searchTerm}
              onChange={handleSearch}
              className="chaumet-input pl-10 pr-4 py-2 w-full"
            />
          </div>

          {selectedUsers.length > 0 && (
            <button
              onClick={() => {
                setDeleteTarget('bulk')
                setShowDeleteConfirm(true)
              }}
              className="chaumet-button border-red-600 text-red-600 group"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center gap-2">
                <FiTrash2 className="h-4 w-4" />
                Delete Selected ({selectedUsers.length})
              </span>
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg font-light">
          {error}
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white rounded-lg border border-neutral-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200">
            <thead className="bg-neutral-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === users.length && users.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-neutral-300 text-neutral-600 focus:ring-neutral-500"
                  />
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest cursor-pointer hover:bg-neutral-100"
                  onClick={() => handleSort('username')}
                >
                  Username
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest cursor-pointer hover:bg-neutral-100"
                  onClick={() => handleSort('email')}
                >
                  Email
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest cursor-pointer hover:bg-neutral-100"
                  onClick={() => handleSort('phone')}
                >
                  Phone
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest cursor-pointer hover:bg-neutral-100"
                  onClick={() => handleSort('role')}
                >
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest">
                  Projects
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest cursor-pointer hover:bg-neutral-100"
                  onClick={() => handleSort('dateCreated')}
                >
                  Date Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-light text-neutral-600 uppercase tracking-widest">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-neutral-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-neutral-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                      className="rounded border-neutral-300 text-neutral-600 focus:ring-neutral-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-light text-neutral-900">{user.username}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-light text-neutral-900">{user.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-light text-neutral-900">{user.phone || 'Not provided'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-light rounded-full ${
                      user.role === 'admin'
                        ? 'bg-neutral-100 text-neutral-800'
                        : user.role === 'user'
                        ? 'bg-neutral-200 text-neutral-800'
                        : 'bg-neutral-300 text-neutral-800'  // client role
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="min-w-[150px]">
                      {user.role === 'client' ? (
                        <div className="text-sm font-light text-neutral-900">
                          {user.projects && user.projects.length > 0 ? (
                            <span className="text-neutral-600">
                              {user.projects.length} project{user.projects.length !== 1 ? 's' : ''} assigned
                            </span>
                          ) : (
                            <span className="text-neutral-400">No projects assigned</span>
                          )}
                        </div>
                      ) : (
                        <span className="text-neutral-400 font-light text-sm">
                          Not applicable
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-light text-neutral-900">
                    {formatDate(user.dateCreated)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-light">
                    <div className="flex items-center gap-2">
                      <Link
                        href={`/admin/users/${user.id}`}
                        className="text-neutral-600 hover:text-neutral-900 transition-colors"
                        title="View/Edit user details"
                      >
                        <FiEye className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => {
                          setDeleteTarget(user.id)
                          setShowDeleteConfirm(true)
                        }}
                        className="text-red-600 hover:text-red-900 transition-colors"
                        title="Delete user"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="bg-white px-4 py-3 border-t border-neutral-200 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="chaumet-button border-neutral-300 text-neutral-700 disabled:opacity-50"
                >
                  <span className="relative z-10">Previous</span>
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.pages))}
                  disabled={currentPage === pagination.pages}
                  className="chaumet-button border-neutral-300 text-neutral-700 disabled:opacity-50 ml-3"
                >
                  <span className="relative z-10">Next</span>
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm font-light text-neutral-700">
                    Showing{' '}
                    <span className="font-light">{(currentPage - 1) * itemsPerPage + 1}</span>
                    {' '}to{' '}
                    <span className="font-light">
                      {Math.min(currentPage * itemsPerPage, pagination.total)}
                    </span>
                    {' '}of{' '}
                    <span className="font-light">{pagination.total}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md -space-x-px">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-neutral-300 bg-white text-sm font-light text-neutral-500 hover:bg-neutral-50 disabled:opacity-50"
                    >
                      <FiChevronLeft className="h-5 w-5" />
                    </button>

                    {[...Array(pagination.pages)].map((_, index) => {
                      const page = index + 1
                      if (
                        page === 1 ||
                        page === pagination.pages ||
                        (page >= currentPage - 1 && page <= currentPage + 1)
                      ) {
                        return (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-light ${
                              page === currentPage
                                ? 'z-10 bg-neutral-50 border-neutral-500 text-neutral-900'
                                : 'bg-white border-neutral-300 text-neutral-500 hover:bg-neutral-50'
                            }`}
                          >
                            {page}
                          </button>
                        )
                      } else if (
                        page === currentPage - 2 ||
                        page === currentPage + 2
                      ) {
                        return (
                          <span
                            key={page}
                            className="relative inline-flex items-center px-4 py-2 border border-neutral-300 bg-white text-sm font-light text-neutral-700"
                          >
                            ...
                          </span>
                        )
                      }
                      return null
                    })}
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.pages))}
                      disabled={currentPage === pagination.pages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-neutral-300 bg-white text-sm font-light text-neutral-500 hover:bg-neutral-50 disabled:opacity-50"
                    >
                      <FiChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-neutral-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border border-neutral-200 w-96 rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <FiTrash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="chaumet-heading text-lg text-neutral-900 mt-4">
                {deleteTarget === 'bulk' ? 'Delete Selected Users' : 'Delete User'}
              </h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm font-light text-neutral-600">
                  {deleteTarget === 'bulk'
                    ? `Are you sure you want to delete ${selectedUsers.length} selected users? This action cannot be undone.`
                    : 'Are you sure you want to delete this user? This action cannot be undone.'
                  }
                </p>
              </div>
              <div className="flex gap-3 mt-4">
                <button
                  onClick={() => {
                    setShowDeleteConfirm(false)
                    setDeleteTarget(null)
                  }}
                  className="chaumet-button border-neutral-300 text-neutral-700 group flex-1"
                >
                  <span className="relative z-10 group-hover:text-white transition-colors duration-500">Cancel</span>
                </button>
                <button
                  onClick={() => {
                    if (deleteTarget === 'bulk') {
                      handleBulkDelete()
                    } else {
                      handleDeleteUser(deleteTarget)
                    }
                  }}
                  className="chaumet-button border-red-600 text-red-600 group flex-1"
                >
                  <span className="relative z-10 group-hover:text-white transition-colors duration-500">Delete</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
