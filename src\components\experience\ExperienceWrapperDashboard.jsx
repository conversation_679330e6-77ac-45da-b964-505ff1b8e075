'use client'
import React from 'react'
import ErrorBoundary from '../ErrorBoundary';
import dynamic from 'next/dynamic';
import ExperienceContextProvider from '@/libs/contextProviders/useExperienceContext';
// import ExperienceUi from './ExperienceUi'
// import ExperienceWorld from './ExperienceWorld'

const ExperienceUi=dynamic(() => import('./ExperienceUi'),{ssr:false})
const ExperienceWorld=dynamic(() => import('./ExperienceWorldDashboard'),{ssr:false})

export default function ExperienceWrapperDashboard({data}) {
  
  // Early return if no data is provided
  if (!data) {
    // console.warn('ExperienceWrapper: No data provided');
    return (
      <div className='flex relative w-full h-full items-center justify-center'>
        <div className="text-gray-500">Loading experience...</div>
      </div>
    );
  }

  return (
    <ExperienceContextProvider>
      <div className='experience-Wrapper text-gray-500 flex relative w-full h-full overflow-hidden'>
        <ExperienceUi data={data}/>
        <ExperienceWorld data={data}/>
      </div>
    </ExperienceContextProvider>
  )
}
